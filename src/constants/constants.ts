export const SUPPORT_EMAIL: string = "<EMAIL>";
export const MAX_RETRY_ATTEMPTS: number = 300000;

interface URLS {
  support: string;
  howToAddDomains: string;
  differenceBetweenDeployments: string;
  forking: {
    learnMore: string;
  };
}

export const URL_LINKS : URLS = {
  support: "https://atlas-kb.com/atlas-e74243keac/articles/769724-credits-and-pricing",
  howToAddDomains: "https://atlas-kb.com/atlas-e74243keac/articles/708702-deployments-and-custom-domains",
  differenceBetweenDeployments: "https://atlas-kb.com/atlas-e74243keac/articles/546580-understanding-preview-vs-deployed-links?preview=rghmutfqzwoexklyusxsfzqpjxbrkv",

  // Fork Modals

  forking: {
    learnMore: "https://atlas-kb.com/atlas-e74243keac/articles/636555-chat-forking",
  },
};

interface CustomText {
  logsHeading: string;
  logsPrompt: string;
  logsIdentifier: string;
}

export const CUSTOM_TEXT : CustomText = {
  logsHeading: "Sharing build error logs when trying to deploy the current app to production. Please analyze these error logs, debug why these are happening and fix them.",
  logsPrompt:"I am using emergent deployments, a native functionality that containerizes and deploys applications to Kubernetes. My current deployment is failing with specific errors that need to be resolved through code modifications.The current environment that you are operating in is a sandboxed environment working with MongoDB , the environment where this application would be deployed operates with dedicated atlas provided mongodb.Please analyze the deployment errors below and modify my application code to resolve all issues preventing successful deployment.You should not make any docker related changes, you should only make code level changes that can make the deployment go through.",
  logsIdentifier: "--logs_identifier--"
}